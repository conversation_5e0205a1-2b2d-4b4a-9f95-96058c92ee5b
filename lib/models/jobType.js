const mongoose = require('mongoose');
const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema
const JobTypeSchema = new mongoose.Schema(
  {
    name: {
      type: String,
    },
    nameAlias: {
      type: String,
    },
    description: {
      type: String,
    },
    unit: {
      type: Schema.Types.ObjectId,
      ref: 'Unit'
    },
    status: {
      type: Number,
      default: 1 // 1: hoạt động, 0: không hoạt động
    },
    quickReport: {
      type: Boolean,
      default: true
    },
    detailReport: {
      type: Boolean,
      default: true
    },
    // Report template configuration cho báo cáo quick
    quickReportTemplate: {
      requiredFields: [{
        type: String
      }],
      optionalFields: [{
        type: String
      }],
      requiresLocation: {
        type: Boolean,
        default: false
      },
      requiresTime: {
        type: Boolean,
        default: false
      },
      chartTypes: [{
        type: String,
        enum: ['line', 'bar', 'pie', 'heatmap', 'highlight', 'document']
      }],
      metrics: {
        type: Schema.Types.Mixed,
        default: {}
      }
    },

    // Detail report template configuration cho báo cáo detail
    detailReportTemplate: {
      requiresCaseCode: {
        type: Boolean,
        default: true
      },
      allowedStatuses: [{
        type: String,
        enum: ['pending', 'in_progress', 'completed', 'cancelled', 'on_hold']
      }],
      requiresLocation: {
        type: Boolean,
        default: false
      },
      requiresTime: {
        type: Boolean,
        default: false
      },
      // Các trường bổ sung có thể cần cho báo cáo detail
      additionalFields: {
        type: Schema.Types.Mixed,
        default: {}
      }
    },
    deletedAt: {
      type: Number // Timestamp khi bị xóa (soft delete)
    },
    createdAt: {
      type: Number,
      default: Date.now
    },
    updatedAt: {
      type: Number,
      default: Date.now
    }
  },
  { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("JobType", JobTypeSchema)
