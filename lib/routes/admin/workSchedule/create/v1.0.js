const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);

const scheduleService = require('../../../../services/scheduleService');
const StatisticsTrigger = require('../../../../utils/statisticsTrigger');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const DateUtils = require('../../../../utils/dateUtils');

/**
 * API tạo lịch làm việc cho cán bộ
 * POST /api/v1.0/work-schedule/create
 *
 * Hỗ trợ hai format cho shifts:
 * - Format cũ: shifts: ['morning', 'afternoon'] (status mặc định = 'scheduled')
 * - Format mới: shifts: [{ type: 'morning', status: 'excused' }, { type: 'afternoon', status: 'scheduled' }]
 */
module.exports = (req, res) => {
  const creatorId = req.user.id;
  const {
    schedules // Mảng lịch chi tiết cho từng ngày: [{ date, userIds, shifts }]
  } = req.body;

  let result;

  const validateParams = (next) => {
    // Schema cho shift object format mới
    const shiftObjectSchema = Joi.object({
      type: Joi.string().valid('morning', 'afternoon').required(),
      status: Joi.string().valid('scheduled', 'excused').default('scheduled').optional()
    });

    // Schema cho shift string format cũ
    const shiftStringSchema = Joi.string().valid('morning', 'afternoon');

    const schema = Joi.object({
      schedules: Joi.array().items(
        Joi.object({
          // Chỉ chấp nhận định dạng DD-MM-YYYY
          date: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).required(),
          userIds: Joi.array().items(Joi.objectId()).min(1).required(),
          // Hỗ trợ cả hai format: string array hoặc object array
          shifts: Joi.array().items(
            Joi.alternatives().try(shiftStringSchema, shiftObjectSchema)
          ).min(1).required()
        })
      ).required()
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Kiểm tra duplicate dates trong schedules
    const dateSet = new Set();
    for (const schedule of schedules) {
      if (dateSet.has(schedule.date)) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Lỗi dữ liệu đầu vào',
            body: `Ngày ${schedule.date} bị trùng lặp trong danh sách lịch. Mỗi ngày chỉ được xuất hiện một lần.`
          }
        });
      }
      dateSet.add(schedule.date);
    }

    // Validate định dạng date cho tất cả schedules
    for (const schedule of schedules) {
      if (!DateUtils.isValidDDMMYYYY(schedule.date)) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Lỗi định dạng ngày',
            body: `Ngày ${schedule.date} không đúng định dạng DD-MM-YYYY`
          }
        });
      }
    }

    next();
  };

  /**
   * Chuẩn hóa dữ liệu shifts để hỗ trợ cả format cũ và mới
   * @param {Array} schedules - Mảng lịch từ client
   * @returns {Array} Mảng lịch đã được chuẩn hóa
   */
  const normalizeSchedulesData = (schedules) => {
    return schedules.map(schedule => {
      const normalizedShifts = schedule.shifts.map(shift => {
        // Nếu shift là string (format cũ), chuyển thành object với status mặc định
        if (typeof shift === 'string') {
          return {
            type: shift,
            status: 'scheduled'
          };
        }
        // Nếu shift đã là object (format mới), giữ nguyên nhưng đảm bảo có status
        return {
          type: shift.type,
          status: shift.status || 'scheduled'
        };
      });

      return {
        ...schedule,
        shifts: normalizedShifts
      };
    });
  };

  const createSchedule = (next) => {
    try {
      // Chuẩn hóa dữ liệu shifts để hỗ trợ cả format cũ và mới
      const normalizedSchedules = normalizeSchedulesData(schedules);

      scheduleService.createFlexibleWorkSchedule(creatorId, normalizedSchedules)
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res.message || MESSAGES.SYSTEM.ERROR
            });
          }

          result = res;

          next();
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: result.message,
      data: result.data
    });

    // Trigger statistics update cho từng schedule được tạo
    if (result.success && result.data && result.data.createdSchedules) {
      result.data.createdSchedules.forEach(schedule => {
        StatisticsTrigger.triggerWorkScheduleUpdate('create', {
          _id: schedule._id,
          user: schedule.user,
          date: schedule.date,
          shifts: schedule.shifts
        });
      });
    }

    // Log hoạt động
    SystemLogModel && SystemLogModel.create({
      user: creatorId,
      action: 'create_work_schedule',
      description: 'Tạo lịch làm việc',
      data: req.body,
      updatedData: result.data
    }, () => { });
  };

  async.waterfall([
    validateParams,
    createSchedule,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};