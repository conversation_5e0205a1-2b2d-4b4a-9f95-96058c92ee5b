const _ = require('lodash');
const async = require('async');
const Joi = require('joi');

const statisticsService = require('../../../services/statisticsService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const config = require('config');

const { Configuration, OpenAIApi } = require("openai");

const configuration = new Configuration({
  apiKey: config.ai.apiKey,
  basePath: config.ai.url,
  baseOptions: {
    headers: {
      'Authorization': `Bearer ${config.ai.apiKey}`,
    },
  },
});

const client = new OpenAIApi(configuration);


module.exports = (req, res) => {
  let {
    prompt = '',
    type = 'general', // 'general', 'attendance', 'performance'
    timeRange = '7days',
    unitIds = []
  } = req.body;

  let statisticsData = {
    "tong_so_vu": 114,
    "theo_khu_vuc": {
      "HungVuong": 23,
      "SoDau": 16,
      "ThuongLy": 22,
      "HaLy": 14,
      "MinhKhai": 21,
      "ThuongLy2": 18
    },
    "chi_tiet": {
      "HungVuong": {
        "theo_ngay": { "T2": 3, "T3": 2, "T4": 4, "T5": 5, "T6": 4, "T7": 2, "CN": 3 },
        "theo_linh_vuc": { "Chay": 7, "MauThuan": 6, "ViPham": 5, "TNGT": 5 }
      },
      "SoDau": {
        "theo_ngay": { "T2": 2, "T3": 2, "T4": 2, "T5": 3, "T6": 3, "T7": 2, "CN": 2 },
        "theo_linh_vuc": { "Chay": 5, "MauThuan": 4, "ViPham": 4, "TNGT": 3 }
      },
      "ThuongLy": {
        "theo_ngay": { "T2": 3, "T3": 3, "T4": 3, "T5": 4, "T6": 3, "T7": 3, "CN": 3 },
        "theo_linh_vuc": { "Chay": 6, "MauThuan": 6, "ViPham": 5, "TNGT": 5 }
      },
      "HaLy": {
        "theo_ngay": { "T2": 2, "T3": 2, "T4": 2, "T5": 2, "T6": 2, "T7": 2, "CN": 2 },
        "theo_linh_vuc": { "Chay": 4, "MauThuan": 3, "ViPham": 4, "TNGT": 3 }
      },
      "MinhKhai": {
        "theo_ngay": { "T2": 3, "T3": 3, "T4": 3, "T5": 3, "T6": 3, "T7": 3, "CN": 3 },
        "theo_linh_vuc": { "Chay": 7, "MauThuan": 5, "ViPham": 5, "TNGT": 4 }
      },
      "ThuongLy2": {
        "theo_ngay": { "T2": 3, "T3": 2, "T4": 3, "T5": 3, "T6": 2, "T7": 2, "CN": 3 },
        "theo_linh_vuc": { "Chay": 6, "MauThuan": 4, "ViPham": 4, "TNGT": 4 }
      }
    }
  }
  let aiResponse = {};

  const validateParams = (next) => {
    const schema = Joi.object({
      prompt: Joi.string().required().min(10).max(1000),
      type: Joi.string().valid('general', 'attendance', 'performance').default('general'),
      timeRange: Joi.string().valid('1day', '7days', '30days', '90days').default('7days'),
      unitIds: Joi.array().items(Joi.string()).default([])
    });

    const { error, value } = schema.validate({ prompt, type, timeRange, unitIds });
    
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: error.details[0].message
      });
    }

    // Update validated values
    prompt = value.prompt;
    type = value.type;
    timeRange = value.timeRange;
    unitIds = value.unitIds;

    next();
  };

  const getStatisticsData = (next) => {
    // Sử dụng dữ liệu mẫu đã có sẵn
    next();
  };

  const callAI = (next) => {
    // Tạo context từ dữ liệu thống kê
    const contextPrompt = `
Dữ liệu đầu vào là số liệu thống kê vụ việc ANTT phát sinh theo khu vực trong tuần.  
Dữ liệu:${JSON.stringify(statisticsData, null, 2)}

Nhiệm vụ của bạn:
1. **So sánh giữa các khu vực**
   - Xác định khu vực có nhiều vụ việc nhất và ít nhất.
   - Tính tỷ lệ chênh lệch, chỉ ra “điểm nóng”.

2. **Phân tích chi tiết các khu vực tiêu biểu (nhiều vụ việc nhất)**
   - Thống kê theo ngày trong tuần (T2–CN).
   - Chỉ ra ngày cao điểm, thấp điểm, biến động.
   - Phân tích theo từng loại vụ việc (cháy, mâu thuẫn, vi phạm, TNGT).

3. **Nhận diện rủi ro & bất thường**
   - Lĩnh vực nào chiếm tỷ trọng lớn → nguy cơ chính.
   - Ngày/khu vực nào vượt ngưỡng trung bình.

4. **Khuyến nghị**
   - Đề xuất bố trí lực lượng, biện pháp phòng ngừa.

Hãy trình bày báo cáo chi tiết, văn phong hành chính – ngắn gọn, rõ ràng, có số liệu minh chứng.`;

    client.createChatCompletion({
      model: config.ai.model,
      messages: [
        {
          role: "system",
          content: "Bạn là chuyên gia phân tích dữ liệu an ninh trật tự cho hệ thống IOC Công an."
        },
        {
          role: "user",
          content: contextPrompt
        }
      ],
      max_tokens: 60000,
      temperature: 0.2
    })
    .then(response => {
      // Kiểm tra an toàn response structure
      if (!response.data || !response.data.choices || response.data.choices.length === 0) {
        console.error('Invalid response structure:', response.data);
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: 'Invalid AI response structure'
        });
      }

      aiResponse = {
        content: response.data.choices[0].message.content,
        usage: response.data.usage,
        model: response.data.model
      };
      next();
    })
    .catch(error => {
      console.error('OpenAI API Error:', error);
      next({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
    });
  };

  const returnResult = (next) => {  
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        report: aiResponse.content,
        metadata: {
          type,
          timeRange,
          unitIds,
          prompt,
          statisticsData,
          aiModel: aiResponse.model,
          usage: aiResponse.usage,
          generatedAt: Date.now()
        }
      }
    });
  };

  async.waterfall([
    // validateParams,
    getStatisticsData,
    callAI,
    returnResult
  ], (err, data) => {
    if (_.isError(err)) {
      global.logger && global.logger.logError([err], req.originalUrl, req.body);
      global.MailUtil && global.MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
