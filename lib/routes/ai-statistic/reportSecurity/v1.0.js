const _ = require('lodash');
const async = require('async');
const Joi = require('joi');

const statisticsService = require('../../../services/statisticsService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const config = require('config');

const { Configuration, OpenAIApi } = require("openai");

const configuration = new Configuration({
  apiKey: config.ai.apiKey,
  basePath: config.ai.url,
  baseOptions: {
    headers: {
      'Authorization': `Bearer ${config.ai.apiKey}`,
    },
  },
});

const client = new OpenAIApi(configuration);


module.exports = (req, res) => {
  let {
    prompt = '',
    type = 'general', // 'general', 'attendance', 'performance'
    timeRange = '7days',
    unitIds = []
  } = req.body;

  let statisticsData = {
    "tong_so_vu": 123,
    "theo_ngay": {
      "T2": 20,
      "T3": 20,
      "T4": 20,
      "T5": 20,
      "T6": 11,
      "T7": 16,
      "CN": 16
    },
    "theo_khu_vuc": {
      "HungVuong": 23,
      "SoDau": 16,
      "ThuongLy": 22,
      "HaLy": 14,
      "MinhKhai": 21,
      "ThuongLy2": 18
    },
    "theo_linh_vuc": {
      "Chay": 26,
      "MauThuan": 22,
      "ViPham": 17,
      "TNGT": 13
    },
    "ghi_chu": "Số liệu theo tuần, thống kê theo ngày (T2–CN), theo khu vực và theo lĩnh vực"
  };
  let aiResponse = {};

  const validateParams = (next) => {
    const schema = Joi.object({
      prompt: Joi.string().required().min(10).max(1000),
      type: Joi.string().valid('general', 'attendance', 'performance').default('general'),
      timeRange: Joi.string().valid('1day', '7days', '30days', '90days').default('7days'),
      unitIds: Joi.array().items(Joi.string()).default([])
    });

    const { error, value } = schema.validate({ prompt, type, timeRange, unitIds });
    
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: error.details[0].message
      });
    }

    // Update validated values
    prompt = value.prompt;
    type = value.type;
    timeRange = value.timeRange;
    unitIds = value.unitIds;

    next();
  };

  const getStatisticsData = (next) => {
    // Sử dụng dữ liệu mẫu đã có sẵn
    next();
  };

  const callAI = (next) => {
    // Tạo context từ dữ liệu thống kê
    const contextPrompt = `
Dữ liệu đầu vào là số liệu thống kê các vụ việc ANTT phát sinh theo tuần.  
Dữ liệu:${JSON.stringify(statisticsData, null, 2)}

Nhiệm vụ của bạn:
1. **Phân tích thống kê & xu hướng**
   - Nêu tổng số, trung bình/ngày, biến động theo từng ngày (T2–CN).
   - Chỉ ra ngày cao điểm và thấp điểm.

2. **Phân tích theo khu vực**
   - So sánh số vụ việc giữa các phường.
   - Xác định điểm nóng, điểm ổn định.

3. **Phân tích theo loại vụ việc**
   - Đánh giá loại vụ việc nào chiếm tỷ trọng cao nhất.
   - Xác định rủi ro chính (cháy, mâu thuẫn, vi phạm, TNGT…).

4. **Phát hiện bất thường & cảnh báo**
   - Ngày/khu vực/lĩnh vực nào vượt ngưỡng trung bình.
   - Cảnh báo nguy cơ tiềm ẩn nếu xu hướng này kéo dài.

5. **Khuyến nghị**
   - Đề xuất phân bổ lực lượng, tăng cường biện pháp phòng ngừa.

Hãy trình bày báo cáo chi tiết, văn phong hành chính – ngắn gọn, rõ ràng, có số liệu minh chứng.`;

    client.createChatCompletion({
      model: config.ai.model,
      messages: [
        {
          role: "system",
          content: "Bạn là chuyên gia phân tích dữ liệu an ninh trật tự cho hệ thống IOC Công an."
        },
        {
          role: "user",
          content: contextPrompt
        }
      ],
      max_tokens: 60000,
      temperature: 0.2
    })
    .then(response => {
      // Kiểm tra an toàn response structure
      if (!response.data || !response.data.choices || response.data.choices.length === 0) {
        console.error('Invalid response structure:', response.data);
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: 'Invalid AI response structure'
        });
      }

      aiResponse = {
        content: response.data.choices[0].message.content,
        usage: response.data.usage,
        model: response.data.model
      };
      next();
    })
    .catch(error => {
      console.error('OpenAI API Error:', error);
      next({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
    });
  };

  const returnResult = (next) => {  
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        report: aiResponse.content,
        metadata: {
          type,
          timeRange,
          unitIds,
          prompt,
          statisticsData,
          aiModel: aiResponse.model,
          usage: aiResponse.usage,
          generatedAt: Date.now()
        }
      }
    });
  };

  async.waterfall([
    // validateParams,
    getStatisticsData,
    callAI,
    returnResult
  ], (err, data) => {
    if (_.isError(err)) {
      global.logger && global.logger.logError([err], req.originalUrl, req.body);
      global.MailUtil && global.MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
