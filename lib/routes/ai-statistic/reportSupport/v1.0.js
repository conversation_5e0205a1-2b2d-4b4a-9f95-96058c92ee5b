const _ = require('lodash');
const async = require('async');
const Joi = require('joi');

const statisticsService = require('../../../services/statisticsService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const config = require('config');

const { Configuration, OpenAIApi } = require("openai");

const configuration = new Configuration({
  apiKey: config.ai.apiKey,
  basePath: config.ai.url,
  baseOptions: {
    headers: {
      'Authorization': `Bearer ${config.ai.apiKey}`,
    },
  },
});

const client = new OpenAIApi(configuration);


module.exports = (req, res) => {
  let {
    prompt = '',
    type = 'general', // 'general', 'attendance', 'performance'
    timeRange = '7days',
    unitIds = []
  } = req.body;

  let statisticsData = {
    "cong_van": {
      "den": [10, 8, 9, 12, 7, 8, 6],
      "di": [8, 7, 9, 8, 7, 9, 8],
      "tra_loi": [9, 8, 8, 9, 7, 8, 7],
      "tong": {
        "den": 60,
        "di": 56,
        "tra_loi": 56
      }
    },
    "can_cuoc_dinh_danh": {
      "can_cuoc": [12, 11, 11, 12, 11, 12, 11],
      "dinh_danh": [18, 20, 19, 18, 20, 18, 20],
      "tong": {
        "can_cuoc": 124,
        "dinh_danh": 125
      }
    },
    "bao_ve_doan": {
      "luot": [22, 19, 18, 19, 20, 18, 18],
      "tong": 124
    },
    "dang_ky_bien_so": {
      "thu_hoi": [11, 10, 9, 10, 10, 10, 9],
      "cap_moi": [18, 19, 20, 19, 19, 19, 20],
      "tong": {
        "thu_hoi": 124,
        "cap_moi": 125
      }
    },
    "ghi_chu": "Dữ liệu theo ngày trong tuần, thứ tự [T2, T3, T4, T5, T6, T7, CN]"
  };
  let aiResponse = {};

  const validateParams = (next) => {
    const schema = Joi.object({
      prompt: Joi.string().required().min(10).max(1000),
      type: Joi.string().valid('general', 'attendance', 'performance').default('general'),
      timeRange: Joi.string().valid('1day', '7days', '30days', '90days').default('7days'),
      unitIds: Joi.array().items(Joi.string()).default([])
    });

    const { error, value } = schema.validate({ prompt, type, timeRange, unitIds });
    
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: error.details[0].message
      });
    }

    // Update validated values
    prompt = value.prompt;
    type = value.type;
    timeRange = value.timeRange;
    unitIds = value.unitIds;

    next();
  };

  const getStatisticsData = (next) => {
    // Sử dụng dữ liệu mẫu đã có sẵn
    next();
  };

  const callAI = (next) => {
    // Tạo context từ dữ liệu thống kê
    const contextPrompt = `
Dữ liệu đầu vào là số liệu thống kê trong tuần về các mảng: công văn, cấp căn cước/định danh điện tử, bảo vệ đoàn công tác, đăng ký biển số xe.  

Dữ liệu:
${JSON.stringify(statisticsData, null, 2)}

Nhiệm vụ của bạn:
1. **Phân tích thống kê & xu hướng**
   - Nêu tổng số, trung bình/ngày, biến động theo ngày (T2–CN).
   - Chỉ ra ngày cao điểm và thấp điểm.

2. **Đánh giá hiệu quả xử lý**
   - Công văn: so sánh công văn đến, đi, trả lời.
   - Căn cước/định danh: so sánh mức độ triển khai, tỷ lệ định danh điện tử/căn cước.
   - Đăng ký biển số: phân tích quan hệ giữa số thu hồi và số cấp mới.
   - Bảo vệ đoàn công tác: xác định áp lực và phân bổ theo ngày.

3. **Phát hiện bất thường & cảnh báo**
   - Ngày nào có số liệu bất thường so với trung bình?
   - Điểm nào cần lưu ý (ví dụ công văn đến nhiều nhưng trả lời ít).

4. **Khuyến nghị & dự báo**
   - Đề xuất phân bổ nhân lực.
   - Gợi ý cải tiến quy trình.
   - Dự báo xu hướng tuần tới nếu giữ nhịp hiện tại.

Hãy trình bày kết quả thành một báo cáo phân tích chi tiết, văn phong hành chính – ngắn gọn, rõ ràng, có số liệu minh chứng.`;

    client.createChatCompletion({
      model: config.ai.model,
      messages: [
        {
          role: "system",
          content: "Bạn là chuyên gia phân tích dữ liệu cho hệ thống IOC Công an."
        },
        {
          role: "user",
          content: contextPrompt
        }
      ],
      max_tokens: 60000,
      temperature: 0.2
    })
    .then(response => {
      // Kiểm tra an toàn response structure
      if (!response.data || !response.data.choices || response.data.choices.length === 0) {
        console.error('Invalid response structure:', response.data);
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: 'Invalid AI response structure'
        });
      }

      aiResponse = {
        content: response.data.choices[0].message.content,
        usage: response.data.usage,
        model: response.data.model
      };
      next();
    })
    .catch(error => {
      console.error('OpenAI API Error:', error);
      next({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
    });
  };

  const returnResult = (next) => {  
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        report: aiResponse.content,
        metadata: {
          type,
          timeRange,
          unitIds,
          prompt,
          statisticsData,
          aiModel: aiResponse.model,
          usage: aiResponse.usage,
          generatedAt: Date.now()
        }
      }
    });
  };

  async.waterfall([
    // validateParams,
    getStatisticsData,
    callAI,
    returnResult
  ], (err, data) => {
    if (_.isError(err)) {
      global.logger && global.logger.logError([err], req.originalUrl, req.body);
      global.MailUtil && global.MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
