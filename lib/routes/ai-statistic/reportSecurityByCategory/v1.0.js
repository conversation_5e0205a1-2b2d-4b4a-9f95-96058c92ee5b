const _ = require('lodash');
const async = require('async');
const Joi = require('joi');

const statisticsService = require('../../../services/statisticsService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const config = require('config');

const { Configuration, OpenAIApi } = require("openai");

const configuration = new Configuration({
  apiKey: config.ai.apiKey,
  basePath: config.ai.url,
  baseOptions: {
    headers: {
      'Authorization': `Bearer ${config.ai.apiKey}`,
    },
  },
});

const client = new OpenAIApi(configuration);


module.exports = (req, res) => {
  let {
    prompt = '',
    type = 'general', // 'general', 'attendance', 'performance'
    timeRange = '7days',
    unitIds = []
  } = req.body;

  let statisticsData = {
  "tong_so_vu": 132,
  "theo_linh_vuc_tong": {
    "Chay": 36,
    "MauThuan": 44,
    "ViPham": 28,
    "TNGT": 24
  },
  "theo_ngay_trong_tuan": {
    "Chay":      { "T2": 4, "T3": 5, "T4": 4, "T5": 6, "T6": 7, "T7": 5, "CN": 5 },
    "MauThuan":  { "T2": 5, "T3": 6, "T4": 7, "T5": 7, "T6": 8, "T7": 6, "CN": 5 },
    "ViPham":    { "T2": 3, "T3": 4, "T4": 4, "T5": 5, "T6": 4, "T7": 4, "CN": 4 },
    "TNGT":      { "T2": 3, "T3": 3, "T4": 4, "T5": 4, "T6": 3, "T7": 3, "CN": 4 }
  },
  "theo_khu_vuc_theo_linh_vuc": {
    "Chay": {
      "HungVuong": 8,
      "MinhKhai": 7,
      "PhanBoiChau": 7,
      "ThuongLy": 6,
      "HaLy": 4,
      "NgoQuyen": 4
    },
    "MauThuan": {
      "HungVuong": 9,
      "MinhKhai": 8,
      "PhanBoiChau": 9,
      "ThuongLy": 6,
      "HaLy": 7,
      "NgoQuyen": 5
    },
    "ViPham": {
      "HungVuong": 6,
      "MinhKhai": 6,
      "PhanBoiChau": 5,
      "ThuongLy": 4,
      "HaLy": 3,
      "NgoQuyen": 4
    },
    "TNGT": {
      "HungVuong": 6,
      "MinhKhai": 5,
      "PhanBoiChau": 5,
      "ThuongLy": 3,
      "HaLy": 2,
      "NgoQuyen": 3
    }
  }
}
  let aiResponse = {};

  const validateParams = (next) => {
    const schema = Joi.object({
      prompt: Joi.string().required().min(10).max(1000),
      type: Joi.string().valid('general', 'attendance', 'performance').default('general'),
      timeRange: Joi.string().valid('1day', '7days', '30days', '90days').default('7days'),
      unitIds: Joi.array().items(Joi.string()).default([])
    });

    const { error, value } = schema.validate({ prompt, type, timeRange, unitIds });
    
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: error.details[0].message
      });
    }

    // Update validated values
    prompt = value.prompt;
    type = value.type;
    timeRange = value.timeRange;
    unitIds = value.unitIds;

    next();
  };

  const getStatisticsData = (next) => {
    // Sử dụng dữ liệu mẫu đã có sẵn
    next();
  };

  const callAI = (next) => {
    // Tạo context từ dữ liệu thống kê
    const contextPrompt = `
Dữ liệu đầu vào là số liệu thống kê vụ việc ANTT phát sinh theo lĩnh vực trong tuần.  
Dữ liệu:${JSON.stringify(statisticsData, null, 2)}

Nhiệm vụ của bạn:

1. **Cơ cấu tổng thể theo lĩnh vực**
   - Tính tổng số và tỷ trọng (%) của từng lĩnh vực (Cháy, Mâu thuẫn, Vi phạm, TNGT) trên tổng số vụ việc.
   - Xếp hạng từ cao xuống thấp, xác định lĩnh vực rủi ro chính.

2. **Xu hướng theo thời gian**
   - Với từng lĩnh vực, phân tích biến động theo ngày trong tuần (T2–CN).
   - Xác định ngày cao điểm, thấp điểm, nhận xét về chu kỳ (ví dụ: cuối tuần tăng mâu thuẫn, giữa tuần tăng cháy).

3. **Thống kê theo khu vực trong từng lĩnh vực**
   - Với mỗi lĩnh vực, phân tích số vụ việc phát sinh tại các khu vực (Hùng Vương, Minh Khai, Phan Bội Châu, Thượng Lý, Hạ Lý, Ngô Quyền).
   - Xác định khu vực nóng nhất và ổn định nhất của từng lĩnh vực.
   - So sánh sự khác biệt giữa các khu vực.

4. **Nhận diện bất thường & cảnh báo**
   - Chỉ ra lĩnh vực/khu vực/ngày có số liệu vượt mức trung bình đáng kể.
   - Đưa giả thuyết nguyên nhân khả dĩ (mật độ dân cư, giao thông, hạ tầng PCCC, tụ tập cộng đồng…).

5. **Khuyến nghị**
   - Đề xuất giải pháp ngắn hạn (tăng cường tuần tra, kiểm tra PCCC, tổ hòa giải).
   - Đề xuất giải pháp trung/dài hạn (ứng dụng AI camera, cảm biến, chương trình cộng đồng).

Hãy trình bày báo cáo chi tiết, văn phong hành chính – ngắn gọn, rõ ràng, có số liệu minh chứng.`;

    client.createChatCompletion({
      model: config.ai.model,
      messages: [
        {
          role: "system",
          content: "Bạn là chuyên gia phân tích dữ liệu an ninh trật tự cho hệ thống IOC Công an."
        },
        {
          role: "user",
          content: contextPrompt
        }
      ],
      max_tokens: 60000,
      temperature: 0.2
    })
    .then(response => {
      // Kiểm tra an toàn response structure
      if (!response.data || !response.data.choices || response.data.choices.length === 0) {
        console.error('Invalid response structure:', response.data);
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: 'Invalid AI response structure'
        });
      }

      aiResponse = {
        content: response.data.choices[0].message.content,
        usage: response.data.usage,
        model: response.data.model
      };
      next();
    })
    .catch(error => {
      console.error('OpenAI API Error:', error);
      next({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
    });
  };

  const returnResult = (next) => {  
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        report: aiResponse.content,
        metadata: {
          type,
          timeRange,
          unitIds,
          prompt,
          statisticsData,
          aiModel: aiResponse.model,
          usage: aiResponse.usage,
          generatedAt: Date.now()
        }
      }
    });
  };

  async.waterfall([
    // validateParams,
    getStatisticsData,
    callAI,
    returnResult
  ], (err, data) => {
    if (_.isError(err)) {
      global.logger && global.logger.logError([err], req.originalUrl, req.body);
      global.MailUtil && global.MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
