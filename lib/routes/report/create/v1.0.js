const _ = require('lodash')
const async = require('async')
const Joi = require('joi')
Joi.objectId = require('joi-objectid')(Joi)
const Report = require('../../../models/report')
const JobType = require('../../../models/jobType')
const User = require('../../../models/user')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const StatisticsTrigger = require('../../../utils/statisticsTrigger')

module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    jobType: jobTypeId,
    title,
    description,
    reportType = 'quick', // Mặc định là quick để tương thích ngược
    metrics,
    details = [],
    caseCode,
    workStatus = 'in_progress'
  } = req.body

  let jobTypeData
  let newReport

  const validateParams = (next) => {
    // Schema cơ bản cho tất cả loại báo cáo
    const baseSchema = {
      jobType: Joi.objectId().required(),
      title: Joi.string().allow(''),
      description: Joi.string().allow(''),
      reportType: Joi.string().valid('quick', 'detail').default('quick'),
      details: Joi.array().items(
        Joi.object({
          time: Joi.number().integer().positive(),
          location: Joi.object({
            address: Joi.string(),
            coordinates: Joi.object({
              lat: Joi.number().required(),
              lng: Joi.number().required()
            })
          })
        })
      ).default([])
    }

    // Schema cho báo cáo quick
    if (reportType === 'quick') {
      baseSchema.metrics = Joi.object().required()
      baseSchema.caseCode = Joi.string().optional()
      baseSchema.workStatus = Joi.string().valid('pending', 'in_progress', 'completed', 'cancelled', 'on_hold').optional()
    }

    // Schema cho báo cáo detail
    else if (reportType === 'detail') {
      baseSchema.caseCode = Joi.string().required()
      baseSchema.workStatus = Joi.string().valid('pending', 'in_progress', 'completed', 'cancelled', 'on_hold').default('in_progress')
      baseSchema.metrics = Joi.object().optional()
      // Báo cáo detail chỉ cho phép tối đa 1 detail
      baseSchema.details = Joi.array().max(1).items(
        Joi.object({
          time: Joi.number().integer().positive(),
          location: Joi.object({
            address: Joi.string(),
            coordinates: Joi.object({
              lat: Joi.number().required(),
              lng: Joi.number().required()
            })
          })
        })
      ).default([])
    }

    const schema = Joi.object(baseSchema)
    const { error } = schema.validate(req.body, { allowUnknown: true })
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next()
  }

  const getUserInf = (next) => {
    User.findOne({
      _id: userId,
      status: 1
    }, "jobTypes units")
      .populate('jobTypes', 'name')
      .lean()
      .exec((err, user) => {
        if (err) {
          return next(err)
        }

        if (!user) {
          return next({
            code: CONSTANTS.CODE.NOT_FOUND,
            message: MESSAGES.USER.NOT_EXISTS
          })
        }

        next(null, user);
      })
  }

  const checkJobType = (user, next) => {
    JobType
      .findOne({
        _id: jobTypeId,
        unit: { $in: user.units },
        status: 1,
        deletedAt: { $exists: false }
      }, 'unit quickReportTemplate detailReportTemplate quickReport detailReport')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err)
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.REPORT.CANNOT_CREATE
          })
        }

        // Kiểm tra JobType có hỗ trợ loại báo cáo này không
        const supportedTypes = []
        if (result.quickReport) supportedTypes.push('quick')
        if (result.detailReport) supportedTypes.push('detail')
        if (!supportedTypes.includes(reportType)) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: {
              head: 'Thông báo',
              body: `Công việc này không hỗ trợ báo cáo ${reportType === 'quick' ? 'số lượng' : 'theo trạng thái'}`
            }
          })
        }

        // Kiểm tra user có quyền tạo báo cáo cho jobType này không
        if (user.jobTypes && user.jobTypes.length > 0) {
          const hasPermission = user.jobTypes.some(jt => jt.toString() === jobTypeId.toString())
          if (!hasPermission) {
            return next({
              code: CONSTANTS.CODE.FAIL,
              message: MESSAGES.REPORT.CANNOT_CREATE
            })
          }
        }

        jobTypeData = result
        next()
      })
  }

  const checkCaseCode = (next) => {
    if (reportType !== 'detail' || !caseCode) {
      return next()
    }

    Report.findOne({
      jobType: jobTypeId,
      caseCode,
      reportType: 'detail'
    }, (err, result) => {
      if (err) {
        return next(err)
      }

      if (result) {
        return next({
          code: CONSTANTS.CODE.FAIL,
          message: {
            head: 'Thông báo',
            body: 'Mã vụ việc đã tồn tại'
          }
        })
      }

      next()
    })
  }

  const validateReport = (next) => {
    // Validation khác nhau cho từng loại báo cáo
    if (reportType === 'quick') {
      return validateQuickReport(next)
    } else if (reportType === 'detail') {
      return validateDetailReport(next)
    }

    next()
  }

  const validateQuickReport = (next) => {
    const template = jobTypeData.quickReportTemplate
    if (!template || !template.metrics) {
      return next() // Nếu không có template thì skip validation
    }

    // Validate required fields
    if (template.requiredFields && template.requiredFields.length > 0) {
      for (const field of template.requiredFields) {
        if (!metrics[field] && metrics[field] !== 0) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: `Vui lòng cung cấp ${template.metrics[field].label}`
            }
          })
        }
      }
    }

    // Validate metric values
    Object.keys(metrics).forEach(key => {
      const metricConfig = template.metrics[key]
      if (metricConfig) {
        const value = metrics[key]
        if (metricConfig.type === 'number') {
          if (typeof value !== 'number' || isNaN(value)) {
            return next({
              code: CONSTANTS.CODE.WRONG_PARAMS,
              message: {
                head: 'Thông báo',
                body: `${metricConfig.label} phải là số`
              }
            })
          }
          if (metricConfig.min !== undefined && value < metricConfig.min) {
            return next({
              code: CONSTANTS.CODE.WRONG_PARAMS,
              message: {
                head: 'Thông báo',
                body: `${metricConfig.label} phải lớn hơn ${metricConfig.min}`
              }
            })
          }
        }
      }
    })

    next()
  }

  const validateQuickDetails = (next) => {
    const template = jobTypeData.quickReportTemplate

    // Nếu template yêu cầu location/time và có số lượng > 1, cần có details
    if (template && (template.requiresLocation || template.requiresTime) && reportType === 'quick') {
      // Tìm metric đầu tiên có giá trị > 1 và requiresLocation = true hoặc requiresTime = true
      let needsDetails = {};
      Object.keys(metrics).map(key => {
        const metricConfig = template.metrics && template.metrics[key] && template.metrics[key].needsDetails
        const value = metrics[key]
        needsDetails = { condition: metricConfig && typeof value === 'number' && value > 1, value }
      })

      if (needsDetails.condition && details.length < needsDetails.value) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Báo cáo này cần nhập chi tiết thời gian/địa điểm cho từng trường hợp'
          }
        })
      }

      // Validate từng detail
      details.forEach((detail, index) => {
        if (template.requiresTime && !detail.time) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: `Chi tiết ${index + 1}: Thiếu thông tin thời gian`
            }
          })
        }

        if (template.requiresLocation && !detail.location) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: `Chi tiết ${index + 1}: Thiếu thông tin địa điểm`
            }
          })
        }
      })
    }

    next()
  }

  const validateDetailReport = (next) => {
    const template = jobTypeData.detailReportTemplate

    // Kiểm tra caseCode bắt buộc
    if (!caseCode || caseCode.trim() === '') {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Mã vụ việc là bắt buộc cho báo cáo theo trạng thái'
        }
      })
    }

    // Kiểm tra workStatus hợp lệ
    const allowedStatuses = template?.allowedStatuses || ['pending', 'in_progress', 'completed', 'cancelled', 'on_hold']
    if (!allowedStatuses.includes(workStatus)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: `Trạng thái công việc không hợp lệ. Các trạng thái cho phép: ${allowedStatuses.join(', ')}`
        }
      })
    }

    // Kiểm tra details (chỉ cho phép tối đa 1 detail)
    if (details.length > 1) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Báo cáo theo trạng thái chỉ cần một địa điểm/thời gian'
        }
      })
    }

    // Validate detail nếu có
    if (details.length === 1 && template) {
      const detail = details[0]

      if (template.requiresTime && !detail.time) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Thiếu thông tin thời gian'
          }
        })
      }

      if (template.requiresLocation && !detail.location) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Thiếu thông tin địa điểm'
          }
        })
      }
    }

    next()
  }

  const createReport = (next) => {
    // Convert coordinates format cho GeoJSON
    const processedDetails = details.map(detail => {
      if (detail.location && detail.location.coordinates) {
        const { lat, lng } = detail.location.coordinates
        return {
          ...detail,
          location: {
            ...detail.location,
            coordinates: [lng, lat], // GeoJSON format: [lng, lat]
            lat: lat, // Giữ lại để tương thích
            lng: lng
          }
        }
      }
      return detail
    })

    const reportData = {
      title,
      description,
      reportType,
      jobType: jobTypeId,
      details: processedDetails,
      createdBy: userId,
      unit: req.user.unit,
      status: 'submitted',
      createdAt: Date.now(),
      updatedAt: Date.now()
    }

    // Thêm các trường đặc biệt cho báo cáo quick
    if (reportType === 'quick') {
      reportData.metrics = metrics || {}
      reportData.workStatus = 'completed'
    }

    // Thêm các trường đặc biệt cho báo cáo detail
    if (reportType === 'detail') {
      reportData.caseCode = caseCode
      reportData.workStatus = workStatus
    }

    Report
      .create(reportData, (err, result) => {
        if (err) {
          return next(err)
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }

        newReport = result
        next()
      })
  }

  const populateReport = (next) => {
    Report
      .findById(newReport._id)
      .populate('jobType', 'name nameAlias description')
      .populate('createdBy', 'name username')
      .populate('unit', 'name nameAlias')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err)
        }

        newReport = result
        next()
      })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: newReport,
      message: {
        head: 'Thông báo',
        body: 'Tạo báo cáo thành công'
      }
    })

    // Trigger statistics update cho document reports
    try {
      StatisticsTrigger.triggerDocumentUpdate('create', newReport);
    } catch (error) {
      console.error('Error triggering document update:', error);
    }

    // Log system action (nếu có SystemLogModel)
    if (global.SystemLogModel) {
      SystemLogModel.create({
        user: userId,
        action: 'create_report',
        description: 'Tạo báo cáo mới',
        data: req.body,
        updatedData: newReport
      }, () => { })
    }
  }

  async.waterfall([
    validateParams,
    getUserInf,
    checkJobType,
    checkCaseCode,
    validateReport,
    validateQuickDetails,
    createReport,
    populateReport,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body)
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    })

    res.json(data || err)
  })
}